# Authentication Utilities

This directory contains authentication utilities for the phonebook application.

## withAuth Higher-Order Function

The `withAuth` function is a higher-order function that wraps API route handlers with authentication logic, eliminating the need to repeat authentication code in every protected route.

### Basic Usage

```typescript
import { withAuth } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';

export const GET = withAuth(async (request, { user }) => {
  // user is guaranteed to be authenticated here
  // user.id, user.email, user.name are available
  
  const data = await getSomeUserData(user.id);
  return NextResponse.json(data);
});

export const POST = withAuth(async (request, { user, session }) => {
  const body = await request.json();
  
  // Process the request with authenticated user
  const result = await createSomething(user.id, body);
  
  return NextResponse.json(result, { status: 201 });
});
```

### Custom Error Handling

For custom error responses, use `withAuthCustom`:

```typescript
import { withAuth<PERSON>ustom } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';

export const GET = withAuthCustom(
  async (request, { user }) => {
    // Your authenticated logic here
    return NextResponse.json({ data: 'success' });
  },
  {
    onUnauthorized: () => NextResponse.json(
      { error: 'Custom unauthorized message' },
      { status: 401 }
    ),
    onError: (error) => NextResponse.json(
      { error: 'Custom error handling' },
      { status: 500 }
    ),
  }
);
```

### Utility Functions

#### getCurrentUser

For contexts where you need to check authentication but don't want to wrap the entire handler:

```typescript
import { getCurrentUser } from '@/lib/auth/withAuth';

export async function someMiddleware(request: NextRequest) {
  const user = await getCurrentUser(request);
  
  if (!user) {
    // Handle unauthenticated user
    return NextResponse.redirect('/login');
  }
  
  // Continue with authenticated user
  return NextResponse.next();
}
```

## Benefits

1. **DRY Principle**: Eliminates repetitive authentication code
2. **Type Safety**: Provides TypeScript types for authenticated user context
3. **Consistent Error Handling**: Standardized 401 responses for unauthorized access
4. **Flexible**: Supports custom error handling when needed
5. **Easy to Use**: Simple wrapper function that maintains Next.js API route patterns

## Migration from Manual Auth

### Before (Manual Authentication)

```typescript
export async function GET(request: NextRequest) {
  const auth = await getAuth();
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  if (!session?.user?.id) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  // Your logic here with session.user.id
  const data = await getUserData(session.user.id);
  return NextResponse.json(data);
}
```

### After (Using withAuth)

```typescript
export const GET = withAuth(async (request, { user }) => {
  // Your logic here with user.id
  const data = await getUserData(user.id);
  return NextResponse.json(data);
});
```

The `withAuth` function handles all the authentication boilerplate, making your code cleaner and more maintainable.
