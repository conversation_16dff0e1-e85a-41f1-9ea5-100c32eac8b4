import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@/lib/auth/config';
import { headers } from 'next/headers';

// Infer the session type from our auth instance
type AuthSession = Awaited<ReturnType<Awaited<ReturnType<typeof getAuth>>['api']['getSession']>>;

export interface AuthContext {
  user: NonNullable<AuthSession>['user'];
  session: NonNullable<AuthSession>['session'];
}

export type AuthenticatedHandler<T = any> = (
  request: NextRequest,
  context: AuthContext,
  routeContext?: { params: T }
) => Promise<NextResponse> | NextResponse;

export type UnauthenticatedHandler<T = any> = (
  request: NextRequest,
  routeContext?: { params: T }
) => Promise<NextResponse> | NextResponse;

/**
 * Higher-order function that wraps API route handlers with authentication
 * 
 * @param handler - The authenticated route handler function
 * @returns A Next.js API route handler with authentication
 * 
 * @example
 * ```typescript
 * // Simple route without parameters
 * export const GET = withAuth(async (request, { user }) => {
 *   // user is guaranteed to be authenticated here
 *   const userContacts = await getUserContacts(user.id);
 *   return NextResponse.json(userContacts);
 * });
 *
 * // Route with parameters (e.g., /api/users/[id]/route.ts)
 * export const GET = withAuth(async (request, { user }, { params }) => {
 *   const { id } = await params; // params is a Promise in App Router
 *   const userData = await getUserById(id);
 *   return NextResponse.json(userData);
 * });
 * ```
 */
export function withAuth<T = any>(
  handler: AuthenticatedHandler<T>
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, routeContext?: { params: T }) => {
    try {
      const auth = await getAuth();
      
      // For GET requests without body, use headers() from next/headers
      // For other requests, use request.headers directly
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const authContext: AuthContext = {
        user: session.user,
        session: session.session,
      };

      return await handler(request, authContext, routeContext);
    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Utility function to get the current authenticated user from a request
 * This is useful for middleware or other contexts where you need to check auth
 * but don't want to wrap the entire handler
 * 
 * @param request - The Next.js request object
 * @returns The authenticated user or null if not authenticated
 */
export async function getCurrentUser(request: NextRequest): Promise<NonNullable<AuthSession>['user'] | null> {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return null;
    }

    return session.user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Alternative withAuth that allows for custom error responses
 * 
 * @param handler - The authenticated route handler function
 * @param options - Configuration options
 * @returns A Next.js API route handler with authentication
 */
export function withAuthCustom<T = any>(
  handler: AuthenticatedHandler<T>,
  options?: {
    onUnauthorized?: () => NextResponse;
    onError?: (error: any) => NextResponse;
  }
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, routeContext?: { params: T }) => {
    try {
      const auth = await getAuth();
      
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return options?.onUnauthorized?.() || NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const authContext: AuthContext = {
        user: session.user,
        session: session.session,
      };

      return await handler(request, authContext, routeContext);
    } catch (error) {
      console.error('Authentication error:', error);
      return options?.onError?.(error) || NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}
