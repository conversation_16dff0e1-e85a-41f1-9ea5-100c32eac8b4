import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@/lib/auth/config';
import { headers } from 'next/headers';

export interface AuthenticatedUser {
  id: string;
  email?: string;
  name?: string;
}

export interface AuthContext {
  user: AuthenticatedUser;
  session: any; // The full session object from better-auth
}

export type AuthenticatedHandler<T = any> = (
  request: NextRequest,
  context: AuthContext,
  params?: T
) => Promise<NextResponse> | NextResponse;

export type UnauthenticatedHandler<T = any> = (
  request: NextRequest,
  params?: T
) => Promise<NextResponse> | NextResponse;

/**
 * Higher-order function that wraps API route handlers with authentication
 * 
 * @param handler - The authenticated route handler function
 * @returns A Next.js API route handler with authentication
 * 
 * @example
 * ```typescript
 * export const GET = withAuth(async (request, { user }) => {
 *   // user is guaranteed to be authenticated here
 *   const userContacts = await getUserContacts(user.id);
 *   return NextResponse.json(userContacts);
 * });
 * ```
 */
export function withAuth<T = any>(
  handler: Authenticated<PERSON>and<PERSON><T>
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, params?: T) => {
    try {
      const auth = await getAuth();
      
      // For GET requests without body, use headers() from next/headers
      // For other requests, use request.headers directly
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const authContext: AuthContext = {
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
        },
        session,
      };

      return await handler(request, authContext, params);
    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Utility function to get the current authenticated user from a request
 * This is useful for middleware or other contexts where you need to check auth
 * but don't want to wrap the entire handler
 * 
 * @param request - The Next.js request object
 * @returns The authenticated user or null if not authenticated
 */
export async function getCurrentUser(request: NextRequest): Promise<AuthenticatedUser | null> {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return null;
    }

    return {
      id: session.user.id,
      email: session.user.email,
      name: session.user.name,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Alternative withAuth that allows for custom error responses
 * 
 * @param handler - The authenticated route handler function
 * @param options - Configuration options
 * @returns A Next.js API route handler with authentication
 */
export function withAuthCustom<T = any>(
  handler: AuthenticatedHandler<T>,
  options?: {
    onUnauthorized?: () => NextResponse;
    onError?: (error: any) => NextResponse;
  }
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, params?: T) => {
    try {
      const auth = await getAuth();
      
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return options?.onUnauthorized?.() || NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const authContext: AuthContext = {
        user: {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
        },
        session,
      };

      return await handler(request, authContext, params);
    } catch (error) {
      console.error('Authentication error:', error);
      return options?.onError?.(error) || NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}
