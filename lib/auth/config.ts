import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";
import { Pool } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import {authSchema} from '@/lib/db/schema';
import { getConfig } from '@/lib/config';

// Auth instance cache
let authInstance: ReturnType<typeof betterAuth> | null = null;

// Create database connection for auth
const createDbConnection = () => {
  const config = getConfig();

  if (!config.DATABASE_URL) {
    throw new Error('DATABASE_URL is required');
  }

  const pool = new Pool({ connectionString: config.DATABASE_URL });
  return drizzle(pool, { schema: authSchema });
};

// Create auth instance
const createAuth = () => {
  const config = getConfig();
  const db = createDbConnection();

  return betterAuth({
    database: drizzleAdapter(db, {
      provider: "pg",
    }),
    secret: config.BETTER_AUTH_SECRET,
    baseURL: config.BETTER_AUTH_URL,
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false,
    },
    socialProviders: {
      google: {
        clientId: config.GOOGLE_CLIENT_ID,
        clientSecret: config.GOOGLE_CLIENT_SECRET,
      },
    },
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
    },
    plugins: [
      nextCookies(), // Must be last plugin for Next.js
    ],
  });
};

// Get auth instance with caching
export const getAuth = async () => {
  if (!authInstance) {
    authInstance = createAuth();
  }
  return authInstance;
};
