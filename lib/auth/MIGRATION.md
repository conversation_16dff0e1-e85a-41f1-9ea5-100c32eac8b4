# Migration Guide: Proper TypeScript Types

This guide shows how we've updated the `withAuth` function to use proper TypeScript types from better-auth instead of `any` types.

## What Changed

### Before (Using `any`)

```typescript
export interface AuthContext {
  user: AuthenticatedUser;
  session: any; // ❌ Using any type
}

export interface AuthenticatedUser {
  id: string;
  email?: string;
  name?: string;
}
```

### After (Using Proper Types)

```typescript
// Infer the session type from our auth instance
type AuthSession = Awaited<ReturnType<Awaited<ReturnType<typeof getAuth>>['api']['getSession']>>;

export interface AuthContext {
  user: NonNullable<AuthSession>['user'];    // ✅ Proper user type
  session: NonNullable<AuthSession>['session']; // ✅ Proper session type
}
```

## Benefits of Proper Types

### 1. **Complete Type Safety**
```typescript
export const GET = withAuth(async (_request, { user, session }) => {
  // ✅ All user properties are properly typed
  console.log(user.id);           // string
  console.log(user.email);        // string
  console.log(user.emailVerified); // boolean
  console.log(user.createdAt);     // Date
  console.log(user.updatedAt);     // Date
  console.log(user.image);         // string | null | undefined

  // ✅ All session properties are properly typed
  console.log(session.id);         // string
  console.log(session.userId);     // string
  console.log(session.expiresAt);  // Date
  console.log(session.token);      // string
  console.log(session.ipAddress);  // string | null | undefined
  console.log(session.userAgent);  // string | null | undefined
});
```

### 2. **IntelliSense Support**
- Full autocomplete for all user and session properties
- Type checking prevents accessing non-existent properties
- Better development experience with IDE support

### 3. **Future-Proof**
- Types automatically update when better-auth updates
- Custom fields added via plugins are automatically included
- No manual type maintenance required

## Migration Steps

If you have existing code using the old `withAuth`, here's how to migrate:

### Step 1: Update Import
```typescript
// No changes needed - same import
import { withAuth } from '@/lib/auth/withAuth';
```

### Step 2: Update Usage (if needed)
```typescript
// Before: Manual object creation
const authContext = {
  user: {
    id: session.user.id,
    email: session.user.email,
    name: session.user.name,
  },
  session: session, // This was wrong anyway
};

// After: Direct usage
const authContext = {
  user: session.user,    // Complete user object
  session: session.session, // Proper session object
};
```

### Step 3: Remove Manual Type Definitions
```typescript
// ❌ Remove these manual definitions
interface AuthenticatedUser {
  id: string;
  email?: string;
  name?: string;
}

// ✅ Types are now automatically inferred from better-auth
```

## Example: Complete Route

```typescript
import { withAuth } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';

export const GET = withAuth(async (_request, { user, session }) => {
  // All properties are properly typed and available
  const userData = {
    id: user.id,
    email: user.email,
    name: user.name,
    emailVerified: user.emailVerified,
    createdAt: user.createdAt,
    sessionId: session.id,
    sessionExpires: session.expiresAt,
  };

  return NextResponse.json(userData);
});

export const POST = withAuth(async (request, { user }) => {
  const body = await request.json();
  
  // Process with fully typed user object
  const result = await processUserData(user.id, body);
  
  return NextResponse.json(result);
});
```

## Type Inference

The new implementation uses TypeScript's type inference to automatically extract the correct types from your better-auth configuration:

```typescript
type AuthSession = Awaited<ReturnType<Awaited<ReturnType<typeof getAuth>>['api']['getSession']>>;
```

This ensures that:
- Types match your exact better-auth setup
- Custom fields from plugins are included
- Types stay in sync with better-auth updates
- No manual type maintenance is required

## Conclusion

The updated `withAuth` function provides:
- ✅ Complete type safety
- ✅ Better developer experience
- ✅ Automatic type inference
- ✅ Future-proof implementation
- ✅ No `any` types

Your existing routes will continue to work, but now with proper TypeScript support!
