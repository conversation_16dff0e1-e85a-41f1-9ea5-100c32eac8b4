import { withAuth } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';

/**
 * Example route demonstrating how to use route parameters with withAuth
 * This route would be accessed at /api/users/[id]
 */

// Define the type for your route parameters
interface RouteParams {
  id: string;
}

export const GET = withAuth(async (request, { user }, routeContext) => {
  // Extract params from routeContext
  if (!routeContext?.params) {
    return NextResponse.json(
      { error: 'Missing route parameters' },
      { status: 400 }
    );
  }

  // In App Router, params is a Promise
  const { id } = await routeContext.params as RouteParams;

  // Validate the ID parameter
  if (!id) {
    return NextResponse.json(
      { error: 'User ID is required' },
      { status: 400 }
    );
  }

  // Example: Only allow users to access their own data or admins to access any
  if (user.id !== id && user.role !== 'admin') {
    return NextResponse.json(
      { error: 'Forbidden: You can only access your own data' },
      { status: 403 }
    );
  }

  // Mock user data - replace with actual database query
  const userData = {
    id,
    requestedBy: {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    },
    message: `User data for ID: ${id}`,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(userData);
});

export const PUT = withAuth(async (request, { user }, routeContext) => {
  if (!routeContext?.params) {
    return NextResponse.json(
      { error: 'Missing route parameters' },
      { status: 400 }
    );
  }

  const { id } = await routeContext.params as RouteParams;
  const body = await request.json();

  // Only allow users to update their own data or admins to update any
  if (user.id !== id && user.role !== 'admin') {
    return NextResponse.json(
      { error: 'Forbidden: You can only update your own data' },
      { status: 403 }
    );
  }

  // Mock update - replace with actual database update
  const updatedUser = {
    id,
    ...body,
    updatedBy: user.id,
    updatedAt: new Date().toISOString(),
  };

  return NextResponse.json({
    message: 'User updated successfully',
    user: updatedUser,
  });
});

export const DELETE = withAuth(async (request, { user }, routeContext) => {
  if (!routeContext?.params) {
    return NextResponse.json(
      { error: 'Missing route parameters' },
      { status: 400 }
    );
  }

  const { id } = await routeContext.params as RouteParams;

  // Only admins can delete users
  if (user.role !== 'admin') {
    return NextResponse.json(
      { error: 'Forbidden: Only admins can delete users' },
      { status: 403 }
    );
  }

  // Prevent self-deletion
  if (user.id === id) {
    return NextResponse.json(
      { error: 'You cannot delete your own account' },
      { status: 400 }
    );
  }

  // Mock deletion - replace with actual database deletion
  return NextResponse.json({
    message: `User ${id} deleted successfully`,
    deletedBy: user.id,
    deletedAt: new Date().toISOString(),
  });
});
