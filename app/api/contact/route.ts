import { getDb, contacts } from '@/lib/db';
import { eq } from 'drizzle-orm';
import { withAuth } from '@/lib/auth/withAuth';
import { NextResponse } from 'next/server';

async function getUserContacts(userId: string) {
  const db = await getDb();
  return await db
    .select()
    .from(contacts)
    .where(eq(contacts.userId, userId))
    .orderBy(contacts.firstName, contacts.lastName);
}

export const GET = withAuth(async (_request, { user }) => {
  const userContacts = await getUserContacts(user.id);
  return NextResponse.json(userContacts);
});